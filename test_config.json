{"gateway": {"ipAddress": "127.0.0.1", "port": 502}, "masters": [{"name": "Master1", "requestInterval": 100, "requests": [{"slaveId": 1, "function": 3, "startAddress": 0, "length": 10}, {"slaveId": 2, "function": 3, "startAddress": 0, "length": 10}, {"slaveId": 3, "function": 3, "startAddress": 0, "length": 10}]}, {"name": "Master2", "requestInterval": 200, "requests": [{"slaveId": 1, "function": 1, "startAddress": 0, "length": 10}, {"slaveId": 2, "function": 1, "startAddress": 0, "length": 10}, {"slaveId": 3, "function": 1, "startAddress": 0, "length": 10}]}], "slaves": [{"name": "Slave1", "ipAddress": "127.0.0.1", "port": 503, "slaveId": 1, "isRtu": false, "holdingRegisters": {"0": 100, "1": 200, "2": 300}, "coils": {"0": true, "1": false, "2": true}}, {"name": "Slave2", "ipAddress": "127.0.0.1", "port": 504, "slaveId": 2, "isRtu": false, "holdingRegisters": {"0": 400, "1": 500, "2": 600}, "coils": {"0": false, "1": true, "2": false}}, {"name": "Slave3-RTU", "ipAddress": "127.0.0.1", "port": 505, "slaveId": 3, "isRtu": true, "comPort": "COM3", "baudRate": 9600, "holdingRegisters": {"0": 700, "1": 800, "2": 900}, "coils": {"0": true, "1": true, "2": false}}], "parameters": {"duration": 300, "warmupTime": 30, "enableLogging": true}}