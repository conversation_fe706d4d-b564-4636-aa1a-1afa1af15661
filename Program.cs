using CommandLine;
using ModbusStressTest.Models;
using ModbusStressTest.Simulators;
using Serilog;
using System.Text.Json;

class Program
{
    public class Options
    {
        [Option('c', "config", Required = true, HelpText = "测试配置文件路径")]
        public string ConfigFile { get; set; } = string.Empty;
    }

    static async Task Main(string[] args)
    {
        // 配置日志
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("modbus_stress_test.log")
            .CreateLogger();

        try
        {
            await Parser.Default.ParseArguments<Options>(args)
                .WithParsedAsync(RunTest);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "测试执行过程中发生错误");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static async Task RunTest(Options options)
    {
        // 读取配置文件
        var configJson = await File.ReadAllTextAsync(options.ConfigFile);
        var config = JsonSerializer.Deserialize<TestConfiguration>(configJson);
        
        if (config == null)
        {
            throw new Exception("无法解析配置文件");
        }

        var testResult = new TestResult
        {
            TestName = Path.GetFileNameWithoutExtension(options.ConfigFile),
            StartTime = DateTime.UtcNow,
            Configuration = config
        };

        // 创建从机模拟器
        var slaves = new List<ModbusSlaveSimulator>();
        foreach (var slaveConfig in config.Slaves)
        {
            try
            {
                var slave = new ModbusSlaveSimulator(slaveConfig, testResult);
                slaves.Add(slave);
                Log.Information("已创建从机模拟器: {SlaveName}", slaveConfig.Name);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "创建从机模拟器失败: {SlaveName}", slaveConfig.Name);
            }
        }

        // 等待从机启动
        await Task.Delay(1000);

        // 创建主机模拟器
        var masters = new List<ModbusMasterSimulator>();
        foreach (var masterConfig in config.Masters)
        {
            try
            {
                var master = new ModbusMasterSimulator(masterConfig, config.Gateway, testResult);
                masters.Add(master);
                Log.Information("已创建主机模拟器: {MasterName}", masterConfig.Name);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "创建主机模拟器失败: {MasterName}", masterConfig.Name);
            }
        }

        // 预热阶段
        Log.Information("开始预热阶段，持续 {WarmupTime} 秒", config.Parameters.WarmupTime);
        await Task.Delay(config.Parameters.WarmupTime * 1000);

        // 正式测试阶段
        Log.Information("开始正式测试，持续 {Duration} 秒", config.Parameters.Duration);
        await Task.Delay(config.Parameters.Duration * 1000);

        // 停止所有模拟器
        foreach (var master in masters)
        {
            master.Stop();
        }
        foreach (var slave in slaves)
        {
            slave.Stop();
        }

        // 记录测试结束时间
        testResult.EndTime = DateTime.UtcNow;

        // 输出测试结果
        Log.Information("测试完成，开始输出结果");
        Log.Information("测试名称: {TestName}", testResult.TestName);
        Log.Information("测试开始时间: {StartTime}", testResult.StartTime);
        Log.Information("测试结束时间: {EndTime}", testResult.EndTime);
        Log.Information("测试持续时间: {Duration} 秒", (testResult.EndTime - testResult.StartTime).TotalSeconds);

        // 计算并输出主机结果
        foreach (var masterResult in testResult.MasterResults.Values)
        {
            // 确保计算正确的统计数据
            masterResult.TotalRequests = masterResult.SuccessfulRequests + masterResult.FailedRequests;
            
            Log.Information("主机 {MasterName} 测试结果:", masterResult.MasterName);
            Log.Information("  总请求数: {TotalRequests}", masterResult.TotalRequests);
            Log.Information("  成功请求数: {SuccessfulRequests}", masterResult.SuccessfulRequests);
            Log.Information("  失败请求数: {FailedRequests}", masterResult.FailedRequests);
            Log.Information("  成功率: {SuccessRate:F2}%", masterResult.SuccessRate);
            Log.Information("  平均响应时间: {AverageResponseTime:F2} ms", masterResult.AverageResponseTime);
            Log.Information("  最小响应时间: {MinResponseTime} ms", masterResult.MinResponseTime);
            Log.Information("  最大响应时间: {MaxResponseTime} ms", masterResult.MaxResponseTime);
        }

        // 计算并输出从机结果
        foreach (var slaveResult in testResult.SlaveResults.Values)
        {
            // 确保计算正确的统计数据
            slaveResult.TotalRequests = slaveResult.SuccessfulRequests + slaveResult.FailedRequests;
            
            Log.Information("从机 {SlaveName} 测试结果:", slaveResult.SlaveName);
            Log.Information("  总请求数: {TotalRequests}", slaveResult.TotalRequests);
            Log.Information("  成功请求数: {SuccessfulRequests}", slaveResult.SuccessfulRequests);
            Log.Information("  失败请求数: {FailedRequests}", slaveResult.FailedRequests);
            Log.Information("  成功率: {SuccessRate:F2}%", slaveResult.SuccessRate);
            Log.Information("  平均响应时间: {AverageResponseTime:F2} ms", slaveResult.AverageResponseTime);
            Log.Information("  最小响应时间: {MinResponseTime} ms", slaveResult.MinResponseTime);
            Log.Information("  最大响应时间: {MaxResponseTime} ms", slaveResult.MaxResponseTime);
        }

        // 保存测试结果到文件
        var resultJson = JsonSerializer.Serialize(testResult, new JsonSerializerOptions { WriteIndented = true });
        var resultFile = $"{testResult.TestName}_result.json";
        await File.WriteAllTextAsync(resultFile, resultJson);
        Log.Information("测试结果已保存到文件: {ResultFile}", resultFile);
    }
} 