using System.Collections.Concurrent;

namespace ModbusStressTest.Models;

public class TestResult
{
    public string TestName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public ConcurrentDictionary<string, MasterResult> MasterResults { get; } = new();
    public ConcurrentDictionary<string, SlaveResult> SlaveResults { get; } = new();
    public TestConfiguration Configuration { get; set; } = new();
}

public class MasterResult
{
    public string MasterName { get; set; } = string.Empty;
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public ConcurrentBag<long> ResponseTimes { get; } = new();
    
    public double AverageResponseTime => ResponseTimes.Any() 
        ? ResponseTimes.Average() 
        : 0;
    
    public long MinResponseTime => ResponseTimes.Any() 
        ? ResponseTimes.Min() 
        : 0;
    
    public long MaxResponseTime => ResponseTimes.Any() 
        ? ResponseTimes.Max() 
        : 0;
    
    public double SuccessRate => TotalRequests > 0 
        ? (double)SuccessfulRequests / TotalRequests * 100 
        : 0;
}

public class SlaveResult
{
    public string SlaveName { get; set; } = string.Empty;
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public ConcurrentBag<long> ResponseTimes { get; } = new();
    
    public double AverageResponseTime => ResponseTimes.Any() 
        ? ResponseTimes.Average() 
        : 0;
    
    public long MinResponseTime => ResponseTimes.Any() 
        ? ResponseTimes.Min() 
        : 0;
    
    public long MaxResponseTime => ResponseTimes.Any() 
        ? ResponseTimes.Max() 
        : 0;
    
    public double SuccessRate => TotalRequests > 0 
        ? (double)SuccessfulRequests / TotalRequests * 100 
        : 0;
} 