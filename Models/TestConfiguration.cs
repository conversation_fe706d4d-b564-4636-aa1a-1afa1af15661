using System.Collections.Generic;

namespace ModbusStressTest.Models;

public class TestConfiguration
{
    public GatewayConfig Gateway { get; set; } = new();
    public List<MasterConfig> Masters { get; set; } = new();
    public List<SlaveConfig> Slaves { get; set; } = new();
    public TestParameters Parameters { get; set; } = new();
}

public class GatewayConfig
{
    public string IpAddress { get; set; } = "127.0.0.1";
    public int Port { get; set; } = 502;
}

public class MasterConfig
{
    public string Name { get; set; } = string.Empty;
    public int RequestInterval { get; set; } = 1000; // 毫秒
    public List<ModbusRequest> Requests { get; set; } = new();
}

public class SlaveConfig
{
    public string Name { get; set; } = string.Empty;
    public string IpAddress { get; set; } = "127.0.0.1";
    public int Port { get; set; } = 502;
    public byte SlaveId { get; set; } = 1;
    public bool IsRtu { get; set; }
    public string? ComPort { get; set; }
    public int BaudRate { get; set; } = 9600;
    public Dictionary<ushort, ushort> HoldingRegisters { get; set; } = new();
    public Dictionary<ushort, bool> Coils { get; set; } = new();
}

public class ModbusRequest
{
    public byte SlaveId { get; set; }
    public ModbusFunction Function { get; set; }
    public ushort StartAddress { get; set; }
    public ushort Length { get; set; }
}

public class TestParameters
{
    public int Duration { get; set; } = 300; // 测试持续时间（秒）
    public int WarmupTime { get; set; } = 30; // 预热时间（秒）
    public bool EnableLogging { get; set; } = true;
}

public enum ModbusFunction
{
    ReadCoils = 1,
    ReadDiscreteInputs = 2,
    ReadHoldingRegisters = 3,
    ReadInputRegisters = 4,
    WriteSingleCoil = 5,
    WriteSingleRegister = 6,
    WriteMultipleCoils = 15,
    WriteMultipleRegisters = 16
} 