using NModbus;

namespace ModbusStressTest.Simulators;

public class DataStore : ISlaveDataStore
{
    public Dictionary<ushort, ushort> HoldingRegisters { get; } = new();
    public Dictionary<ushort, bool> Coils { get; } = new();
    public Dictionary<ushort, ushort> InputRegisters { get; } = new();
    public Dictionary<ushort, bool> DiscreteInputs { get; } = new();
    
    public IModbusMessageDataCollection<bool> CoilDiscretes => new ModbusDataCollection<bool>(address => 
        Coils.TryGetValue(address, out var value) ? value : false);
    
    public IModbusMessageDataCollection<bool> InputDiscretes => new ModbusDataCollection<bool>(address => 
        DiscreteInputs.TryGetValue(address, out var value) ? value : false);
    
    public IModbusMessageDataCollection<ushort> HoldingRegisters16 => new ModbusDataCollection<ushort>(address => 
        HoldingRegisters.TryGetValue(address, out var value) ? value : (ushort)0);
    
    public IModbusMessageDataCollection<ushort> InputRegisters16 => new ModbusDataCollection<ushort>(address => 
        InputRegisters.TryGetValue(address, out var value) ? value : (ushort)0);
}

public class ModbusDataCollection<T> : IModbusMessageDataCollection<T>
{
    private readonly Func<ushort, T> _valueProvider;

    public ModbusDataCollection(Func<ushort, T> valueProvider)
    {
        _valueProvider = valueProvider;
    }

    public T this[ushort address] 
    { 
        get => _valueProvider(address);
        set { /* 写入操作在此实现中不需要 */ }
    }
}