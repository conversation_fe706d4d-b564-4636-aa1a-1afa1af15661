using NModbus;

namespace ModbusStressTest.Simulators;

/// <summary>
/// 自定义数据存储类，实现ISlaveDataStore接口
/// </summary>
public class DataStore : ISlaveDataStore
{
    private readonly Dictionary<ushort, ushort> _holdingRegisters = new();
    private readonly Dictionary<ushort, bool> _coils = new();
    private readonly Dictionary<ushort, ushort> _inputRegisters = new();
    private readonly Dictionary<ushort, bool> _discreteInputs = new();

    public IPointSource<bool> CoilDiscretes { get; }
    public IPointSource<bool> CoilInputs { get; }
    public IPointSource<ushort> HoldingRegisters { get; }
    public IPointSource<ushort> InputRegisters { get; }

    public DataStore()
    {
        CoilDiscretes = new PointSource<bool>(_coils);
        CoilInputs = new PointSource<bool>(_discreteInputs);
        HoldingRegisters = new PointSource<ushort>(_holdingRegisters);
        InputRegisters = new PointSource<ushort>(_inputRegisters);
    }

    /// <summary>
    /// 设置保持寄存器的值
    /// </summary>
    public void SetHoldingRegister(ushort address, ushort value)
    {
        _holdingRegisters[address] = value;
    }

    /// <summary>
    /// 设置线圈的值
    /// </summary>
    public void SetCoil(ushort address, bool value)
    {
        _coils[address] = value;
    }

    /// <summary>
    /// 设置输入寄存器的值
    /// </summary>
    public void SetInputRegister(ushort address, ushort value)
    {
        _inputRegisters[address] = value;
    }

    /// <summary>
    /// 设置离散输入的值
    /// </summary>
    public void SetDiscreteInput(ushort address, bool value)
    {
        _discreteInputs[address] = value;
    }
}

/// <summary>
/// 自定义Modbus点源实现
/// </summary>
public class PointSource<T> : IPointSource<T>
{
    private readonly Dictionary<ushort, T> _data;
    private readonly T _defaultValue;

    public PointSource(Dictionary<ushort, T> data)
    {
        _data = data;
        _defaultValue = default(T)!;
    }

    public T[] ReadPoints(ushort startAddress, ushort numberOfPoints)
    {
        var result = new T[numberOfPoints];
        for (ushort i = 0; i < numberOfPoints; i++)
        {
            var address = (ushort)(startAddress + i);
            result[i] = _data.TryGetValue(address, out var value) ? value : _defaultValue;
        }
        return result;
    }

    public void WritePoints(ushort startAddress, T[] points)
    {
        for (ushort i = 0; i < points.Length; i++)
        {
            var address = (ushort)(startAddress + i);
            _data[address] = points[i];
        }
    }
}
