using System.Net.Sockets;
using ModbusStressTest.Models;
using NModbus;
using Serilog;

namespace ModbusStressTest.Simulators;

public class ModbusMasterSimulator : IDisposable
{
    private readonly MasterConfig _config;
    private readonly IModbusMaster _master;
    private readonly TcpClient _client;
    private readonly CancellationTokenSource _cts;
    private readonly Task _simulationTask;
    private readonly TestResult _testResult;
    private readonly MasterResult _masterResult;

    public ModbusMasterSimulator(MasterConfig config, GatewayConfig gatewayConfig, TestResult testResult)
    {
        _config = config;
        _testResult = testResult;
        _client = new TcpClient();
        _cts = new CancellationTokenSource();
        
        _masterResult = new MasterResult { MasterName = config.Name };
        _testResult.MasterResults.TryAdd(config.Name, _masterResult);
        
        _client.Connect(gatewayConfig.IpAddress, gatewayConfig.Port);
        var factory = new ModbusFactory();
        _master = factory.CreateMaster(_client);
        
        _simulationTask = Task.Run(SimulateAsync);
    }

    private async Task SimulateAsync()
    {
        try
        {
            while (!_cts.Token.IsCancellationRequested)
            {
                foreach (var request in _config.Requests)
                {
                    try
                    {
                        var startTime = DateTime.UtcNow;
                        
                        switch (request.Function)
                        {
                            case ModbusFunction.ReadCoils:
                                await _master.ReadCoilsAsync(request.SlaveId, request.StartAddress, request.Length);
                                break;
                            case ModbusFunction.ReadDiscreteInputs:
                                await _master.ReadInputsAsync(request.SlaveId, request.StartAddress, request.Length);
                                break;
                            case ModbusFunction.ReadHoldingRegisters:
                                await _master.ReadHoldingRegistersAsync(request.SlaveId, request.StartAddress, request.Length);
                                break;
                            case ModbusFunction.ReadInputRegisters:
                                await _master.ReadInputRegistersAsync(request.SlaveId, request.StartAddress, request.Length);
                                break;
                            default:
                                Log.Warning("Unsupported Modbus function: {Function}", request.Function);
                                continue;
                        }

                        var endTime = DateTime.UtcNow;
                        var responseTime = (long)(endTime - startTime).TotalMilliseconds;
                        
                        _masterResult.ResponseTimes.Add(responseTime);
                        _masterResult.SuccessfulRequests++;
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException)
                    {
                        Log.Error(ex, "Error executing Modbus request for master {MasterName}", _config.Name);
                        _masterResult.FailedRequests++;
                    }
                    
                    _masterResult.TotalRequests++;
                    
                    try
                    {
                        await Task.Delay(_config.RequestInterval, _cts.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常取消，退出循环
                        break;
                    }
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不需要处理
        }
    }

    public void Stop()
    {
        _cts.Cancel();
        _simulationTask.Wait();
    }

    public void Dispose()
    {
        Stop();
        _client.Dispose();
        _cts.Dispose();
    }
} 