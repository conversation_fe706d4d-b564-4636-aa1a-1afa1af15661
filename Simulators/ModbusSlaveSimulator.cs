using System.IO.Ports;
using System.Net.Sockets;
using ModbusStressTest.Models;
using NModbus;
using Serilog;

namespace ModbusStressTest.Simulators;

public class ModbusSlaveSimulator : IDisposable
{
    private readonly SlaveConfig _config;
    private readonly IModbusSlave _slave;
    private readonly TcpListener? _tcpListener;
    private readonly SerialPort? _serialPort;
    private readonly CancellationTokenSource _cts;
    private readonly Task _simulationTask;
    private readonly TestResult _testResult;
    private readonly SlaveResult _slaveResult;

    public ModbusSlaveSimulator(SlaveConfig config, TestResult testResult)
    {
        _config = config;
        _testResult = testResult;
        _cts = new CancellationTokenSource();
        
        _slaveResult = new SlaveResult { SlaveName = config.Name };
        _testResult.SlaveResults.TryAdd(config.Name, _slaveResult);

        var factory = new ModbusFactory();
        var dataStore = new DataStore();
        
        // 初始化保持寄存器数据
        foreach (var register in config.HoldingRegisters)
        {
            dataStore.HoldingRegisters[register.Key] = register.Value;
        }
        
        // 初始化线圈数据
        foreach (var coil in config.Coils)
        {
            dataStore.Coils[coil.Key] = coil.Value;
        }

        if (config.IsRtu)
        {
            if (string.IsNullOrEmpty(config.ComPort))
                throw new ArgumentException("COM port must be specified for RTU mode");
                
            _serialPort = new SerialPort(config.ComPort, config.BaudRate);
            _serialPort.Open();
            _slave = factory.CreateSlave(config.SlaveId, dataStore);
        }
        else
        {
            _tcpListener = new TcpListener(System.Net.IPAddress.Parse(config.IpAddress), config.Port);
            _tcpListener.Start();
            _slave = factory.CreateSlave(config.SlaveId, dataStore);
        }
        
        _simulationTask = Task.Run(SimulateAsync);
    }

    private async Task SimulateAsync()
    {
        try
        {
            while (!_cts.Token.IsCancellationRequested)
            {
                var startTime = DateTime.UtcNow;
                
                try
                {
                    if (_config.IsRtu && _serialPort is not null)
                    {
                        // RTU模式下的处理
                        var buffer = new byte[1024];
                        var bytesRead = await _serialPort.BaseStream.ReadAsync(buffer, 0, buffer.Length, _cts.Token);
                        if (bytesRead > 0)
                        {
                            var request = buffer.Take(bytesRead).ToArray();
                            var response = _slave.ApplyRequest(request);
                            if (response is not null)
                            {
                                await _serialPort.BaseStream.WriteAsync(response, 0, response.Length, _cts.Token);
                            }
                        }
                    }
                    else if (_tcpListener is not null)
                    {
                        // TCP模式下的处理
                        var client = await _tcpListener.AcceptTcpClientAsync(_cts.Token);
                        using var stream = client.GetStream();
                        var buffer = new byte[1024];
                        var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, _cts.Token);
                        if (bytesRead > 0)
                        {
                            var request = buffer.Take(bytesRead).ToArray();
                            var response = _slave.ApplyRequest(request);
                            if (response is not null)
                            {
                                await stream.WriteAsync(response, 0, response.Length, _cts.Token);
                            }
                        }
                    }
                    
                    var endTime = DateTime.UtcNow;
                    var responseTime = (long)(endTime - startTime).TotalMilliseconds;
                    
                    _slaveResult.ResponseTimes.Add(responseTime);
                    _slaveResult.SuccessfulRequests++;
                }
                catch (Exception ex) when (ex is not OperationCanceledException)
                {
                    Log.Error(ex, "Error in Modbus slave {SlaveName}", _config.Name);
                    _slaveResult.FailedRequests++;
                }
                
                _slaveResult.TotalRequests++;
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不需要处理
        }
    }

    public void Stop()
    {
        _cts.Cancel();
        _simulationTask.Wait();
    }

    public void Dispose()
    {
        Stop();
        _tcpListener?.Stop();
        _serialPort?.Dispose();
        _cts.Dispose();
    }
} 