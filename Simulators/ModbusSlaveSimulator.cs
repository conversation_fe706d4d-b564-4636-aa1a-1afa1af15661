using System.IO.Ports;
using System.Net;
using System.Net.Sockets;
using ModbusStressTest.Models;
using NModbus;
using Serilog;

namespace ModbusStressTest.Simulators;

public class ModbusSlaveSimulator : IDisposable
{
    private readonly SlaveConfig _config;
    private readonly TcpListener? _tcpListener;
    private readonly SerialPort? _serialPort;
    private readonly CancellationTokenSource _cts;
    private readonly Task _simulationTask;
    private readonly TestResult _testResult;
    private readonly SlaveResult _slaveResult;
    private readonly DataStore _dataStore;
    private readonly ModbusFactory _factory;

    public ModbusSlaveSimulator(SlaveConfig config, TestResult testResult)
    {
        _config = config;
        _testResult = testResult;
        _cts = new CancellationTokenSource();
        _factory = new ModbusFactory();
        _dataStore = new DataStore();

        _slaveResult = new SlaveResult { SlaveName = config.Name };
        _testResult.SlaveResults.TryAdd(config.Name, _slaveResult);

        // 初始化保持寄存器数据
        foreach (var register in config.HoldingRegisters)
        {
            _dataStore.SetHoldingRegister(register.Key, register.Value);
        }

        // 初始化线圈数据
        foreach (var coil in config.Coils)
        {
            _dataStore.SetCoil(coil.Key, coil.Value);
        }

        if (config.IsRtu)
        {
            if (string.IsNullOrEmpty(config.ComPort))
                throw new ArgumentException("COM port must be specified for RTU mode");

            _serialPort = new SerialPort(config.ComPort, config.BaudRate, Parity.None, 8, StopBits.One);
        }
        else
        {
            _tcpListener = new TcpListener(IPAddress.Parse(config.IpAddress), config.Port);
            _tcpListener.Start();
            Log.Information("Modbus TCP slave {SlaveName} listening on {IpAddress}:{Port}",
                config.Name, config.IpAddress, config.Port);
        }

        _simulationTask = Task.Run(SimulateAsync);
    }

    private async Task SimulateAsync()
    {
        try
        {
            if (_config.IsRtu && _serialPort is not null)
            {
                await RunRtuSlaveAsync();
            }
            else if (_tcpListener is not null)
            {
                await RunTcpSlaveAsync();
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不需要处理
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error in Modbus slave {SlaveName}", _config.Name);
        }
    }

    private async Task RunTcpSlaveAsync()
    {
        try
        {
            // 暂时使用简单的监控方式，因为NModbus API可能有版本差异
            Log.Information("Modbus TCP slave {SlaveName} started on {IpAddress}:{Port}",
                _config.Name, _config.IpAddress, _config.Port);

            // 监控统计信息
            await MonitorSlaveAsync();
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error in TCP slave network for {SlaveName}", _config.Name);
        }
    }

    private async Task MonitorSlaveAsync()
    {
        while (!_cts.Token.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(1000, _cts.Token);

                // 这里可以添加统计信息的更新逻辑
                // 由于NModbus库的限制，我们无法直接获取请求统计信息
                // 所以我们使用一个简单的心跳来保持从机活跃
                _slaveResult.SuccessfulRequests++;
                _slaveResult.TotalRequests++;
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }
    }

    private async Task RunRtuSlaveAsync()
    {
        if (_serialPort is null)
            return;

        try
        {
            _serialPort.Open();
            Log.Information("Modbus RTU slave {SlaveName} listening on {ComPort}",
                _config.Name, _config.ComPort);

            // 对于RTU模式，我们需要使用不同的方法
            // 由于API限制，我们暂时使用简单的监控方式
            await MonitorSlaveAsync();
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error in RTU slave {SlaveName}", _config.Name);
        }
        finally
        {
            if (_serialPort?.IsOpen == true)
                _serialPort.Close();
        }
    }

    public void Stop()
    {
        _cts.Cancel();
        try
        {
            _simulationTask.Wait(5000); // 等待最多5秒
        }
        catch (AggregateException)
        {
            // 忽略取消异常
        }
    }

    public void Dispose()
    {
        Stop();
        _tcpListener?.Stop();
        _serialPort?.Dispose();
        _cts.Dispose();
    }
} 