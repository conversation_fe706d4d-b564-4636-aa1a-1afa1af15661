# Modbus网关压力测试工具

这是一个用于测试Modbus网关性能的工具，可以模拟多个Modbus主机和从机，测试网关的吞吐量和响应时间。

## 功能特点

- 支持同时模拟多个Modbus主机和从机
- 支持Modbus-TCP和Modbus-RTU通讯
- 可配置测试参数（持续时间、预热时间等）
- 详细的测试结果统计（响应时间、成功率等）
- 支持JSON格式的配置文件
- 完整的日志记录

## 系统要求

- .NET 9.0 或更高版本
- Windows/Linux/macOS

## 安装

1. 克隆代码库
2. 使用Visual Studio或命令行构建项目：
   ```bash
   dotnet build
   ```

## 使用方法

1. 准备测试配置文件（JSON格式），参考`test_config.json`示例
2. 运行测试：
   ```bash
   dotnet run -- -c test_config.json
   ```

## 配置文件说明

配置文件使用JSON格式，包含以下主要部分：

### 网关配置
```json
{
  "gateway": {
    "ipAddress": "127.0.0.1",
    "port": 502
  }
}
```

### 主机配置
```json
{
  "masters": [
    {
      "name": "Master1",
      "requestInterval": 100,
      "requests": [
        {
          "slaveId": 1,
          "function": 3,
          "startAddress": 0,
          "length": 10
        }
      ]
    }
  ]
}
```

### 从机配置
```json
{
  "slaves": [
    {
      "name": "Slave1",
      "ipAddress": "127.0.0.1",
      "port": 503,
      "slaveId": 1,
      "isRtu": false,
      "holdingRegisters": {
        "0": 100,
        "1": 200
      },
      "coils": {
        "0": true,
        "1": false
      }
    }
  ]
}
```

### 测试参数
```json
{
  "parameters": {
    "duration": 300,
    "warmupTime": 30,
    "enableLogging": true
  }
}
```

## 测试结果

测试完成后，工具会生成以下输出：

1. 控制台输出：实时显示测试进度和结果
2. 日志文件：`modbus_stress_test.log`
3. 结果文件：`{test_name}_result.json`

测试结果包含：
- 总请求数
- 成功/失败请求数
- 成功率
- 平均响应时间
- 最小响应时间
- 最大响应时间

## 注意事项

1. 确保网关和从机的IP地址和端口配置正确
2. 如果使用RTU模式，需要正确配置串口参数
3. 建议先用较小的测试规模进行验证
4. 注意网络带宽和系统资源的使用情况

## 许可证

MIT License 